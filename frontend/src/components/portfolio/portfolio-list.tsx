'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { api } from '@/lib/api';
import { MSME } from '@/types';
import { ArrowLeft, Building2, MapPin, Calendar, TrendingUp, TrendingDown, Minus, AlertTriangle } from 'lucide-react';

export function PortfolioList() {
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const filterRisk = searchParams.get('filter');

  useEffect(() => {
    async function fetchPortfolio() {
      try {
        setLoading(true);
        const data = await api.getPortfolio();
        setMsmes(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch portfolio');
      } finally {
        setLoading(false);
      }
    }

    fetchPortfolio();
  }, []);

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      case 'stable': return <Minus className="h-4 w-4 text-gray-500" />;
      default: return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const filteredMsmes = filterRisk 
    ? msmes.filter(msme => msme.risk_band === filterRisk)
    : msmes;

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-4">
          <div className="h-8 bg-muted rounded w-1/4 animate-pulse"></div>
          <Card>
            <CardHeader>
              <div className="h-6 bg-muted rounded w-1/3 animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-12 bg-muted rounded animate-pulse"></div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Portfolio</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              MSME Portfolio
              {filterRisk && (
                <span className="ml-2 text-lg font-normal text-muted-foreground">
                  - {getRiskLabel(filterRisk)} Risk
                </span>
              )}
            </h1>
            <p className="text-muted-foreground">
              {filteredMsmes.length} of {msmes.length} MSMEs
              {filterRisk && (
                <Button 
                  variant="link" 
                  className="p-0 ml-2 h-auto"
                  onClick={() => router.push('/portfolio')}
                >
                  Clear filter
                </Button>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Portfolio Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Portfolio Overview
          </CardTitle>
          <CardDescription>
            Click on any MSME to view detailed information and score breakdown
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Business Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Score</TableHead>
                <TableHead>Risk</TableHead>
                <TableHead>Trend</TableHead>
                <TableHead>Signals</TableHead>
                <TableHead>Last Activity</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMsmes.map((msme) => (
                <TableRow 
                  key={msme.msme_id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => router.push(`/msme/${msme.msme_id}`)}
                >
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <span>{msme.name}</span>
                      <div className="flex gap-1 mt-1">
                        {msme.tags.slice(0, 2).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {msme.business_type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span className="text-sm">{msme.location}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-mono font-semibold">
                      {msme.current_score}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getRiskBadgeVariant(msme.risk_band)}>
                      {getRiskLabel(msme.risk_band)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(msme.score_trend)}
                      <span className="text-sm capitalize">
                        {msme.score_trend || 'stable'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span>{msme.signals_count}</span>
                      {msme.recent_nudges > 0 && (
                        <Badge variant="outline" className="text-xs">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          {msme.recent_nudges}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {new Date(msme.last_signal_date).toLocaleDateString()}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredMsmes.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No MSMEs found
              {filterRisk && ` with ${getRiskLabel(filterRisk).toLowerCase()} risk`}.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
