'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { api } from '@/lib/api';
import { MSME, ScoreDetails } from '@/types';
import { ArrowLeft, Building2, MapPin, Calendar, TrendingUp, TrendingDown, Minus, AlertTriangle, BarChart3 } from 'lucide-react';

interface MSMEDetailProps {
  msmeId: string;
}

export function MSMEDetail({ msmeId }: MSMEDetailProps) {
  const [msme, setMsme] = useState<MSME | null>(null);
  const [scoreDetails, setScoreDetails] = useState<ScoreDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchMSMEData() {
      try {
        setLoading(true);
        const [msmeData, scoreData] = await Promise.all([
          api.getMSME(msmeId),
          api.getMSMEScore(msmeId)
        ]);
        setMsme(msmeData);
        setScoreDetails(scoreData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch MSME data');
      } finally {
        setLoading(false);
      }
    }

    fetchMSMEData();
  }, [msmeId]);

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      case 'stable': return <Minus className="h-4 w-4 text-gray-500" />;
      default: return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="h-8 bg-muted rounded w-1/4 animate-pulse"></div>
          <div className="grid gap-6 md:grid-cols-2">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-6 bg-muted rounded w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-20 bg-muted rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading MSME Data</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!msme || !scoreDetails) return null;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/portfolio">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Portfolio
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
              <Building2 className="h-8 w-8" />
              {msme.name}
            </h1>
            <div className="flex items-center gap-4 mt-2">
              <div className="flex items-center gap-1 text-muted-foreground">
                <MapPin className="h-4 w-4" />
                {msme.location}
              </div>
              <Badge variant="outline" className="capitalize">
                {msme.business_type}
              </Badge>
              <div className="flex gap-1">
                {msme.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Score Overview */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Credit Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-primary mb-2">
              {scoreDetails.current_score}
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={getRiskBadgeVariant(scoreDetails.risk_band)}>
                {getRiskLabel(scoreDetails.risk_band)} Risk
              </Badge>
              {getTrendIcon(msme.score_trend)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Signals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold mb-2">
              {scoreDetails.signals_count}
            </div>
            <p className="text-sm text-muted-foreground">
              Data points collected
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {new Date(msme.last_signal_date).toLocaleDateString()}
              </span>
            </div>
            {msme.recent_nudges > 0 && (
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <span className="text-sm text-destructive">
                  {msme.recent_nudges} recent alerts
                </span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Score Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Score Breakdown</CardTitle>
          <CardDescription>
            Detailed analysis of credit score components
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-semibold mb-2">Score Components</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Base Score:</span>
                  <span className="font-mono">{scoreDetails.score_breakdown.base_score}</span>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>GST Penalty:</span>
                  <span className="font-mono">-{scoreDetails.score_breakdown.gst_penalty}</span>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>UPI Penalty:</span>
                  <span className="font-mono">-{scoreDetails.score_breakdown.upi_penalty}</span>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>Reviews Penalty:</span>
                  <span className="font-mono">-{scoreDetails.score_breakdown.reviews_penalty}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold text-lg">
                  <span>Final Score:</span>
                  <span className="font-mono text-primary">{scoreDetails.current_score}</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Analysis Details</h4>
              <div className="space-y-2">
                {Object.entries(scoreDetails.score_breakdown.details).map(([key, value]) => (
                  <div key={key} className="p-2 bg-muted rounded text-sm">
                    <span className="font-medium capitalize">{key}:</span> {value}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Actions</CardTitle>
          <CardDescription>
            Manage this MSME profile and send notifications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap">
            <Button variant="outline" disabled>
              Send Nudge
            </Button>
            <Button variant="outline" disabled>
              View Signal History
            </Button>
            <Button variant="outline" disabled>
              Generate Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center text-sm text-muted-foreground">
        Last updated: {new Date(scoreDetails.last_updated).toLocaleString()}
      </div>
    </div>
  );
}
