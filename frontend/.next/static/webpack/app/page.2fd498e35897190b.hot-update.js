"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nclass ApiError extends Error {\n    constructor(message, status){\n        super(message), this.status = status;\n        this.name = 'ApiError';\n    }\n}\nasync function fetchApi(endpoint) {\n    try {\n        console.log(\"Fetching: \".concat(API_BASE_URL).concat(endpoint));\n        const response = await fetch(\"\".concat(API_BASE_URL).concat(endpoint), {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        console.log(\"Response status: \".concat(response.status));\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n            throw new ApiError(\"HTTP error! status: \".concat(response.status), response.status);\n        }\n        const data = await response.json();\n        console.log(\"Response data:\", data);\n        return data;\n    } catch (error) {\n        console.error('API fetch error:', error);\n        if (error instanceof ApiError) {\n            throw error;\n        }\n        throw new ApiError(\"Network error: \".concat(error instanceof Error ? error.message : 'Unknown error'), 0);\n    }\n}\nconst api = {\n    // Dashboard Analytics\n    async getAnalytics () {\n        return fetchApi('/dashboard/analytics');\n    },\n    // Portfolio Management\n    async getPortfolio () {\n        return fetchApi('/dashboard/portfolio');\n    },\n    // MSME Management\n    async getMSME (id) {\n        return fetchApi(\"/msme/\".concat(id));\n    },\n    async getMSMEScore (id) {\n        return fetchApi(\"/msme/\".concat(id, \"/score\"));\n    },\n    // Health check\n    async healthCheck () {\n        return fetchApi('/health');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFFQSxNQUFNQSxlQUFlQyx1QkFBK0IsSUFBSSxDQUF1QjtBQUUvRSxNQUFNRyxpQkFBaUJDO0lBQ3JCQyxZQUFZQyxPQUFlLEVBQUUsTUFBcUIsQ0FBRTtRQUNsRCxLQUFLLENBQUNBLGVBRDRCQyxTQUFBQTtRQUVsQyxJQUFJLENBQUNDLElBQUksR0FBRztJQUNkO0FBQ0Y7QUFFQSxlQUFlQyxTQUFZQyxRQUFnQjtJQUN6QyxJQUFJO1FBQ0ZDLFFBQVFDLEdBQUcsQ0FBQyxhQUE0QkYsT0FBZlgsY0FBd0IsT0FBVFc7UUFDeEMsTUFBTUcsV0FBVyxNQUFNQyxNQUFNLEdBQWtCSixPQUFmWCxjQUF3QixPQUFUVyxXQUFZO1lBQ3pESyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFFQUwsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQyxPQUFoQkMsU0FBU04sTUFBTTtRQUUvQyxJQUFJLENBQUNNLFNBQVNJLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1MLFNBQVNNLElBQUk7WUFDckNSLFFBQVFTLEtBQUssQ0FBQyxjQUFtQ0YsT0FBckJMLFNBQVNOLE1BQU0sRUFBQyxPQUFlLE9BQVZXO1lBQ2pELE1BQU0sSUFBSWYsU0FBUyx1QkFBdUMsT0FBaEJVLFNBQVNOLE1BQU0sR0FBSU0sU0FBU04sTUFBTTtRQUM5RTtRQUVBLE1BQU1jLE9BQU8sTUFBTVIsU0FBU1MsSUFBSTtRQUNoQ1gsUUFBUUMsR0FBRyxDQUFFLGtCQUFpQlM7UUFDOUIsT0FBT0E7SUFDVCxFQUFFLE9BQU9ELE9BQU87UUFDZFQsUUFBUVMsS0FBSyxDQUFDLG9CQUFvQkE7UUFDbEMsSUFBSUEsaUJBQWlCakIsVUFBVTtZQUM3QixNQUFNaUI7UUFDUjtRQUNBLE1BQU0sSUFBSWpCLFNBQVMsa0JBQTJFLE9BQXpEaUIsaUJBQWlCaEIsUUFBUWdCLE1BQU1kLE9BQU8sR0FBRyxrQkFBbUI7SUFDbkc7QUFDRjtBQUVPLE1BQU1pQixNQUFNO0lBQ2pCLHNCQUFzQjtJQUN0QixNQUFNQztRQUNKLE9BQU9mLFNBQW9CO0lBQzdCO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1nQjtRQUNKLE9BQU9oQixTQUFpQjtJQUMxQjtJQUVBLGtCQUFrQjtJQUNsQixNQUFNaUIsU0FBUUMsRUFBVTtRQUN0QixPQUFPbEIsU0FBZSxTQUFZLE9BQUhrQjtJQUNqQztJQUVBLE1BQU1DLGNBQWFELEVBQVU7UUFDM0IsT0FBT2xCLFNBQXVCLFNBQVksT0FBSGtCLElBQUc7SUFDNUM7SUFFQSxlQUFlO0lBQ2YsTUFBTUU7UUFDSixPQUFPcEIsU0FBOEM7SUFDdkQ7QUFDRixFQUFFIiwic291cmNlcyI6WyIvVXNlcnMvbWFuaWt1bWFyZ291bmkvRG9jdW1lbnRzL0NyZWRpdC1DaGFrcmEtRmluYWwvY3JlZGl0LWNoYWtyYS9mcm9udGVuZC9zcmMvbGliL2FwaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNU01FLCBBbmFseXRpY3MsIFNjb3JlRGV0YWlscyB9IGZyb20gJ0AvdHlwZXMnO1xuXG5jb25zdCBBUElfQkFTRV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjgwMDAnO1xuXG5jbGFzcyBBcGlFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IobWVzc2FnZTogc3RyaW5nLCBwdWJsaWMgc3RhdHVzOiBudW1iZXIpIHtcbiAgICBzdXBlcihtZXNzYWdlKTtcbiAgICB0aGlzLm5hbWUgPSAnQXBpRXJyb3InO1xuICB9XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGZldGNoQXBpPFQ+KGVuZHBvaW50OiBzdHJpbmcpOiBQcm9taXNlPFQ+IHtcbiAgdHJ5IHtcbiAgICBjb25zb2xlLmxvZyhgRmV0Y2hpbmc6ICR7QVBJX0JBU0VfVVJMfSR7ZW5kcG9pbnR9YCk7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9JHtlbmRwb2ludH1gLCB7XG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGNvbnNvbGUubG9nKGBSZXNwb25zZSBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgY29uc29sZS5lcnJvcihgQVBJIEVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c30gLSAke2Vycm9yVGV4dH1gKTtcbiAgICAgIHRocm93IG5ldyBBcGlFcnJvcihgSFRUUCBlcnJvciEgc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gLCByZXNwb25zZS5zdGF0dXMpO1xuICAgIH1cblxuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgY29uc29sZS5sb2coYFJlc3BvbnNlIGRhdGE6YCwgZGF0YSk7XG4gICAgcmV0dXJuIGRhdGE7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignQVBJIGZldGNoIGVycm9yOicsIGVycm9yKTtcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBBcGlFcnJvcikge1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICAgIHRocm93IG5ldyBBcGlFcnJvcihgTmV0d29yayBlcnJvcjogJHtlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJ31gLCAwKTtcbiAgfVxufVxuXG5leHBvcnQgY29uc3QgYXBpID0ge1xuICAvLyBEYXNoYm9hcmQgQW5hbHl0aWNzXG4gIGFzeW5jIGdldEFuYWx5dGljcygpOiBQcm9taXNlPEFuYWx5dGljcz4ge1xuICAgIHJldHVybiBmZXRjaEFwaTxBbmFseXRpY3M+KCcvZGFzaGJvYXJkL2FuYWx5dGljcycpO1xuICB9LFxuXG4gIC8vIFBvcnRmb2xpbyBNYW5hZ2VtZW50XG4gIGFzeW5jIGdldFBvcnRmb2xpbygpOiBQcm9taXNlPE1TTUVbXT4ge1xuICAgIHJldHVybiBmZXRjaEFwaTxNU01FW10+KCcvZGFzaGJvYXJkL3BvcnRmb2xpbycpO1xuICB9LFxuXG4gIC8vIE1TTUUgTWFuYWdlbWVudFxuICBhc3luYyBnZXRNU01FKGlkOiBzdHJpbmcpOiBQcm9taXNlPE1TTUU+IHtcbiAgICByZXR1cm4gZmV0Y2hBcGk8TVNNRT4oYC9tc21lLyR7aWR9YCk7XG4gIH0sXG5cbiAgYXN5bmMgZ2V0TVNNRVNjb3JlKGlkOiBzdHJpbmcpOiBQcm9taXNlPFNjb3JlRGV0YWlscz4ge1xuICAgIHJldHVybiBmZXRjaEFwaTxTY29yZURldGFpbHM+KGAvbXNtZS8ke2lkfS9zY29yZWApO1xuICB9LFxuXG4gIC8vIEhlYWx0aCBjaGVja1xuICBhc3luYyBoZWFsdGhDaGVjaygpOiBQcm9taXNlPHsgc3RhdHVzOiBzdHJpbmc7IHNlcnZpY2U6IHN0cmluZyB9PiB7XG4gICAgcmV0dXJuIGZldGNoQXBpPHsgc3RhdHVzOiBzdHJpbmc7IHNlcnZpY2U6IHN0cmluZyB9PignL2hlYWx0aCcpO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbIkFQSV9CQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiQXBpRXJyb3IiLCJFcnJvciIsImNvbnN0cnVjdG9yIiwibWVzc2FnZSIsInN0YXR1cyIsIm5hbWUiLCJmZXRjaEFwaSIsImVuZHBvaW50IiwiY29uc29sZSIsImxvZyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwib2siLCJlcnJvclRleHQiLCJ0ZXh0IiwiZXJyb3IiLCJkYXRhIiwianNvbiIsImFwaSIsImdldEFuYWx5dGljcyIsImdldFBvcnRmb2xpbyIsImdldE1TTUUiLCJpZCIsImdldE1TTUVTY29yZSIsImhlYWx0aENoZWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});