"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nclass ApiError extends Error {\n    constructor(message, status){\n        super(message), this.status = status;\n        this.name = 'ApiError';\n    }\n}\nasync function fetchApi(endpoint) {\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL).concat(endpoint), {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new ApiError(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText), response.status);\n        }\n        return await response.json();\n    } catch (error) {\n        if (error instanceof ApiError) {\n            throw error;\n        }\n        throw new ApiError(\"Network error: \".concat(error instanceof Error ? error.message : 'Unknown error'), 0);\n    }\n}\nconst api = {\n    // Dashboard Analytics\n    async getAnalytics () {\n        return fetchApi('/dashboard/analytics');\n    },\n    // Portfolio Management\n    async getPortfolio () {\n        return fetchApi('/dashboard/portfolio');\n    },\n    // MSME Management\n    async getMSME (id) {\n        return fetchApi(\"/msme/\".concat(id));\n    },\n    async getMSMEScore (id) {\n        return fetchApi(\"/msme/\".concat(id, \"/score\"));\n    },\n    // Health check\n    async healthCheck () {\n        return fetchApi('/health');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});